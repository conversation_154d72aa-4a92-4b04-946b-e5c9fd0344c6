<script lang="ts">
	import type { NavLink } from "$types";
	import NavDropdownButton from "./NavDropdownButton.svelte";

	/**
	 * Props interface for the navigation link list component
	 */
	interface Props {
		links: NavLink[];
		activeDropdownId: string | null;
		onTriggerEnter: (linkId: string) => void;
		onTriggerLeave: () => void;
		variant?: "default" | "ghost" | "outline";
		class?: string;
		linkClass?: string;
	}

	let {
		links,
		activeDropdownId,
		onTriggerEnter,
		onTriggerLeave,
		variant = "ghost",
		class: className = "",
		linkClass = ""
	}: Props = $props();
</script>

<!-- Navigation links container -->
<div class={className}>
	{#each links as link (link.label)}
		<NavDropdownButton
			{link}
			{variant}
			class={linkClass}
			{activeDropdownId}
			{onTriggerEnter}
			{onTriggerLeave}
		/>
	{/each}
</div>
