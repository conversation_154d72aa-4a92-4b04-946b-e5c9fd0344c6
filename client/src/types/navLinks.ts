import type { Component } from "svelte";

/**
 * Represents the kind of a leaf navigation link.
 *
 * - "default": A standard navigation link.
 * - "login": A link to a login page, usually styled differently.
 * - "talk-to-sales": A link for contacting sales, typically highlighted.
 */
export type NavLinkKind = "default" | "login" | "schedule-demo";

/**
 * Shared base type for all navigation links.
 */
interface BaseNavLink {
	label: string;
	tagline?: string;
}

/**
 * A navigation link that directly leads to a URL (i.e., has an `href`)
 * and does **not** have any children.
 */
export interface LeafNavLink extends BaseNavLink {
	href: string;
	kind: NavLinkKind;
	children?: never;
}

/**
 * A navigation link that groups other links under it (i.e., has `children`)
 * and does **not** have an `href` or `kind`.
 */
interface ParentNavLink extends BaseNavLink {
	children: NavLink[];
	href?: never;
	kind?: never;
}

/**
 * Union type representing either a leaf link or a parent link.
 */
export type NavLink = LeafNavLink | ParentNavLink;


/**
 * Represents a social media link with a name, href, and icon.
 */
export type SocialMediaLink = {
	name: string;
	href: string;
	icon: Component;
};
