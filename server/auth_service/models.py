import uuid
from django.db import models
from django.contrib.auth.models import AbstractBaseU<PERSON>, BaseUserManager
from django.utils import timezone


# === Custom User Manager ===
class CustomUserManager(BaseUserManager):
    """
    Manager to create users and superusers.
    """
    def create_user(self, username, email, password=None, **extra_fields):
        if not email:
            raise ValueError("Users must have an email address")
        email = self.normalize_email(email)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email, password=None, **extra_fields):
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')

        return self.create_user(username, email, password, **extra_fields)


# === User Model ===
class User(AbstractBaseUser):
    """
    Custom user model using UUID as primary key.

    Fields:
        - username: unique username for login
        - email: unique email address
        - password: hashed password (handled by AbstractBaseUser)
        - user_role: FK to UserRole for permissions
        - is_active: account status
        - is_staff: admin site access flag
        - created_at, updated_at: timestamps
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = models.CharField(max_length=150, unique=True)
    email = models.EmailField(unique=True)
    password = models.CharField(max_length=128)  # handled by AbstractBaseUser
    last_login = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    user_role = models.ForeignKey('UserRole', on_delete=models.SET_NULL, null=True, blank=True)

    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)

    objects = CustomUserManager()

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email']

    def __str__(self):
        return self.username


# === User Role Model ===
class UserRole(models.Model):
    """
    Represents a role grouping permissions for users.

    Fields:
        - role_name: unique role identifier (e.g., 'admin')
        - role_description: optional description
        - created_at, updated_at: timestamps
    """
    user_role_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    role_name = models.CharField(max_length=50, unique=True)
    role_description = models.TextField(blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.role_name


# === Permission Model ===
class Permission(models.Model):
    """
    Defines a permission assigned to a UserRole.

    Fields:
        - permission_name: unique permission identifier (e.g., 'edit_user')
        - permission_description: optional description
        - user_role: FK linking to UserRole
        - created_at, updated_at: timestamps
    """
    permission_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    permission_name = models.CharField(max_length=100, unique=True)
    permission_description = models.TextField(blank=True)
    user_role = models.ForeignKey(UserRole, on_delete=models.CASCADE, related_name='permissions')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.permission_name
